#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to remove the duplicate "null null" entry for <PERSON><PERSON><PERSON>
 * Keeping the properly set up account: user_wfnkv8znb_1754322738860
 * Removing the duplicate: user_30pNedmlrT0FmilnOOjprh04Ezt
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  console.error('   NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅' : '❌')
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅' : '❌')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

interface KhodrCleanup {
  keepUserId: string
  keepName: string
  removeUserId: string
  removeName: string
  email: string
}

const khodrCleanup: KhodrCleanup = {
  keepUserId: 'user_wfnkv8znb_1754322738860',
  keepName: 'Khodr Hassoun',
  removeUserId: 'user_30pNedmlrT0FmilnOOjprh04Ezt',
  removeName: 'null null',
  email: '<EMAIL>'
}

async function removeKhodrDuplicate(): Promise<boolean> {
  console.log('🔄 Starting Khodr Hassoun duplicate cleanup...')
  console.log(`📧 Email: ${khodrCleanup.email}`)
  console.log(`✅ Keeping: ${khodrCleanup.keepName} (${khodrCleanup.keepUserId})`)
  console.log(`❌ Removing: ${khodrCleanup.removeName} (${khodrCleanup.removeUserId})`)

  try {
    // Step 1: Verify both records exist
    const { data: keepRecord, error: keepError } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('user_id', khodrCleanup.keepUserId)
      .single()

    if (keepError) {
      console.error(`❌ Failed to find record to keep: ${keepError.message}`)
      return false
    }

    const { data: removeRecord, error: removeError } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('user_id', khodrCleanup.removeUserId)
      .single()

    if (removeError) {
      console.error(`❌ Failed to find record to remove: ${removeError.message}`)
      return false
    }

    console.log(`📋 Found record to keep: ${keepRecord.full_name} (${keepRecord.role})`)
    console.log(`📋 Found record to remove: ${removeRecord.full_name} (${removeRecord.role})`)

    // Step 2: Check for any employee-manager relationships using the duplicate ID
    const { data: employeeRelations, error: relError } = await supabase
      .from('appy_employee_managers')
      .select('*')
      .eq('manager_id', khodrCleanup.removeUserId)

    if (relError) {
      console.log(`⚠️  Warning: Failed to check employee relationships: ${relError.message}`)
    } else if (employeeRelations && employeeRelations.length > 0) {
      console.log(`🔄 Found ${employeeRelations.length} employee relationships to update`)
      
      // Update employee-manager relationships to use the correct ID
      const { error: updateRelError } = await supabase
        .from('appy_employee_managers')
        .update({ manager_id: khodrCleanup.keepUserId })
        .eq('manager_id', khodrCleanup.removeUserId)

      if (updateRelError) {
        console.error(`❌ Failed to update employee relationships: ${updateRelError.message}`)
        return false
      }

      console.log(`✅ Updated employee-manager relationships`)
    } else {
      console.log(`✅ No employee relationships to update`)
    }

    // Step 3: Check for any appraisal references using the duplicate ID
    const { data: appraisals, error: appraisalError } = await supabase
      .from('appy_appraisals')
      .select('*')
      .eq('manager_id', khodrCleanup.removeUserId)

    if (appraisalError) {
      console.log(`⚠️  Warning: Failed to check appraisal references: ${appraisalError.message}`)
    } else if (appraisals && appraisals.length > 0) {
      console.log(`🔄 Found ${appraisals.length} appraisal references to update`)
      
      // Update appraisal manager references to use the correct ID
      const { error: updateAppraisalError } = await supabase
        .from('appy_appraisals')
        .update({ manager_id: khodrCleanup.keepUserId })
        .eq('manager_id', khodrCleanup.removeUserId)

      if (updateAppraisalError) {
        console.error(`❌ Failed to update appraisal references: ${updateAppraisalError.message}`)
        return false
      }

      console.log(`✅ Updated appraisal manager references`)
    } else {
      console.log(`✅ No appraisal references to update`)
    }

    // Step 4: Check if the duplicate exists in employees table
    const { data: employeeRecord, error: empError } = await supabase
      .from('appy_employees')
      .select('*')
      .eq('user_id', khodrCleanup.removeUserId)
      .single()

    if (employeeRecord && !empError) {
      console.log(`📋 Found duplicate in employees table: ${employeeRecord.full_name}`)
      
      // Update employee record to use the correct user_id
      const { error: empUpdateError } = await supabase
        .from('appy_employees')
        .update({ user_id: khodrCleanup.keepUserId })
        .eq('user_id', khodrCleanup.removeUserId)

      if (empUpdateError) {
        console.log(`⚠️  Warning: Failed to update employee record: ${empUpdateError.message}`)
      } else {
        console.log(`✅ Updated employee record`)
      }
    }

    // Step 5: Delete the duplicate manager record
    const { error: deleteError } = await supabase
      .from('appy_managers')
      .delete()
      .eq('user_id', khodrCleanup.removeUserId)

    if (deleteError) {
      console.error(`❌ Failed to delete duplicate record: ${deleteError.message}`)
      return false
    }

    console.log(`✅ Successfully deleted duplicate record`)

    console.log(`\n🎉 Successfully cleaned up Khodr Hassoun duplicates!`)
    console.log(`   Kept: ${khodrCleanup.keepName} (${khodrCleanup.keepUserId})`)
    console.log(`   Removed: ${khodrCleanup.removeName} (${khodrCleanup.removeUserId})`)

    return true

  } catch (error) {
    console.error('❌ Unexpected error:', error)
    return false
  }
}

// CLI usage
if (require.main === module) {
  console.log('🚀 Khodr Hassoun Duplicate Cleanup Script')
  console.log('=' .repeat(50))
  
  removeKhodrDuplicate()
    .then(success => {
      if (success) {
        console.log('\n✅ Cleanup completed successfully!')
      } else {
        console.log('\n❌ Cleanup failed!')
      }
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('💥 Script failed:', error)
      process.exit(1)
    })
}

export { removeKhodrDuplicate }
